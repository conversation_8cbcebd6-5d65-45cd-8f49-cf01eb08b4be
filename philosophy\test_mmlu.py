from openai import OpenAI
import json
import time
import re
from multiprocessing import Pool
import os
from time import sleep

openai_api  = 'sk-5nAhYDW77Qy5UtzVfhqMuOr0XHw4M6F5f6Oa0j0hcT8BZ2Px'
INVALID_ANS = 'INVALID'

def extract_mmlu_answer(completion):
    """
    从模型输出中提取MMLU答案
    支持多种格式：
    1. "answer is: A"
    2. "(a)"
    3. 直接输出A、B、C、D
    """
    if not completion:
        return INVALID_ANS
        
    # 将文本转换为大写
    text = completion.upper()
    
    # 1. 尝试匹配 "answer is: A" 格式
    matches = re.findall(r"ANSWER IS:?\s*(.*)", text)
    if matches:
        answer = matches[-1].strip().strip(".")
        if answer in 'ABCD':
            return ord(answer) - ord('A')
    
    # 2. 尝试匹配 "(A)" 格式
    match = re.search(r'\(([A-D])\)', text)
    if match:
        return ord(match.group(1)) - ord('A')
    
    # 3. 从后往前查找第一个A、B、C、D
    for char in reversed(text):
        if char in 'ABCD':
            return ord(char) - ord('A')
            
    return INVALID_ANS

def normalize_answer(answer):
    """
    标准化答案格式
    """
    if isinstance(answer, str):
        # 移除括号，转换为大写
        answer = answer.replace("(", "").replace(")", "").upper().strip()
        # 如果是字母，转换为数字
        if answer in 'ABCD':
            return ord(answer) - ord('A')
    return answer

def check_answer(true_answer, predicted_answer):
    """
    比较预测答案和正确答案
    """
    # 标准化两个答案
    true_answer = normalize_answer(true_answer)
    predicted_answer = normalize_answer(predicted_answer)
    
    # 比较答案
    return true_answer == predicted_answer

def process_with_persona(item_with_index):
    """
    处理单个问题
    """
    index,item,persona = item_with_index
    choices_str = "\n".join([f"{chr(65+i)}. {choice}" for i, choice in enumerate(item['choices'])])
    question = f"{item['question']}\n{choices_str}"
    true_answer = item["answer"]
    
    client = OpenAI(api_key=openai_api, base_url="https://api2.aigcbest.top/v1")

    for attempt in range(3):
        try:
            response = client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": f"You are {persona}. Your responses should closely mirror the knowledge and abilities of this persona."}, 
                    {"role": "user", "content": f"Answer the given multiple choice question and show your work. The answer can only be an option like (A), (B), (C), (D). You need to output the answer in your final sentence like ‘‘Therefore, the answer is ...’’  Question: {question}"},
                ],
                stream=False,
                temperature=0.0
            )
            raw_output = response.choices[0].message.content.strip()
            predicted_answer = extract_mmlu_answer(raw_output)
            
            is_correct = check_answer(true_answer, predicted_answer)
            return {
                "index": index + 1,
                "question": question,
                "true_answer": true_answer,
                "predicted_answer": predicted_answer,
                "is_correct": is_correct,
                "raw_output": raw_output,
                "persona": persona['name']
            }
        except Exception as e:
            if attempt < 2:
                sleep(1)
            else:
                return {
                    "index": index + 1,
                    "question": question,
                    "true_answer": true_answer,
                    "predicted_answer": "ERROR",
                    "is_correct": False,
                    "error": str(e),
                    "persona": persona['name']
                }

def load_personas():
    with open("persona.json", "r", encoding="utf-8") as f:
        data = json.load(f)
    # 返回分类信息和persona列表
    return data    
    
if __name__ == "__main__":
    data = []
    with open("mmlu_philosophy.jsonl","r",encoding="utf-8") as f:
        for line in f:
            data.append(json.loads(line.strip()))
    
    categories = load_personas()
    print(f"\n总共加载了 {len(categories)} 个分类")
    
    num_workers = min(os.cpu_count(), 5)
    total_number = len(data)
    print(f"总共加载了 {total_number} 道题目")
    
    all_results = {}
    if os.path.exists("all_persona_mmlu.json"):
        with open("all_persona_mmlu.json", "r", encoding="utf-8") as f:
            all_results = json.load(f)
        print(f"\n已加载已有的测试结果")
    
    for category in categories:
        category_name = category["category"]
        personas = category["personas"]
        
        if category_name not in all_results:
            all_results[category_name] = {}
        
        remaining_personas = [p for p in personas if p['name'] not in all_results[category_name]]
        print(f"\n分类 '{category_name}' 剩余 {len(remaining_personas)} 个persona待测试")
        
        for persona in remaining_personas:
            print(f"\n开始测试 persona: {persona['name']}")
            print("-" * 50)
            
            tasks = [(i, item, persona) for i, item in enumerate(data)]

            start = time.time()
            print(f"正在处理 {total_number} 道题目...")

            with Pool(processes=num_workers) as pool:
                results = pool.imap(process_with_persona, tasks)
                
                right_count = 0
                correct_questions = []  # 存储当前persona答对的题目编号
                
                for result in results:
                    if result["is_correct"]:
                        right_count += 1
                        correct_questions.append(result["index"])
                    # 每个问题只输出编号、预测结果和是否正确
                    print(f"Q{result['index']}: {result['predicted_answer']} ({'✓' if result['is_correct'] else '✗'})")
            
            end = time.time()
            accuracy = right_count / total_number
            
            # 保存当前persona的结果到对应分类下
            all_results[category_name][persona['name']] = {
                "accuracy": accuracy,
                "correct_count": right_count,
                "correct_questions": correct_questions
            }
            
            with open("all_persona_results_philosophy.json", "w", encoding="utf-8") as f:
                json.dump(all_results, f, ensure_ascii=False, indent=2)
                
            print(f"\n{persona['name']} 测试完成！")
            print(f"正确率: {accuracy:.2%}")
            print(f"答对题目数: {right_count}/{total_number}")
            print("=" * 50)

    # 打印总结
    print("\n所有persona测试完成！")
    print("\n各分类准确率排名：")
    for category_name, category_results in all_results.items():
        print(f"\n{category_name}:")
        sorted_results = sorted(category_results.items(), key=lambda x: x[1]["accuracy"], reverse=True)
        for persona, result in sorted_results:
            print(f"  {persona}: {result['accuracy']:.2%}")
